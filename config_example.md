# 币安API配置指南

## 1. 获取币安API密钥

### 步骤：
1. 登录 [币安官网](https://www.binance.com)
2. 点击右上角头像 → **API管理**
3. 点击 **创建API**
4. 输入API标签名称（如：freqtrade）
5. 完成安全验证（邮箱/短信验证）
6. 记录下生成的 **API Key** 和 **Secret Key**

### 权限设置：
- ✅ **现货交易** (Spot Trading) - 必需
- ✅ **读取** (Read) - 必需
- ❌ **期货交易** (Futures) - 可选
- ❌ **提现** (Withdraw) - 不建议开启

## 2. 配置文件修改

在 `user_data/config.json` 文件中找到 `exchange` 部分：

```json
{
    "exchange": {
        "name": "binance",
        "key": "你的API_KEY",
        "secret": "你的SECRET_KEY",
        "ccxt_config": {},
        "ccxt_async_config": {},
        "pair_whitelist": [
            "BTC/USDT",
            "ETH/USDT",
            "ADA/USDT"
        ]
    }
}
```

## 3. 安全配置建议

### IP白名单设置：
在币安API设置中，建议添加IP白名单：
- 如果在本地运行：添加您的公网IP
- 如果在服务器运行：添加服务器IP

### 权限最小化：
- 只开启必要的权限
- 不要开启提现权限
- 定期轮换API密钥

## 4. 测试配置

配置完成后，可以使用以下命令测试连接：

```bash
# 激活环境
source /Users/<USER>/anaconda3/bin/activate freqtrade_conda

# 测试交易所连接
freqtrade test-pairlist --config user_data/config.json

# 下载数据测试
freqtrade download-data --config user_data/config.json --days 1
```

## 5. 从模拟交易切换到真实交易

### 重要配置更改：

1. **关闭模拟交易**：
```json
{
    "dry_run": false,  // 改为 false
    "dry_run_wallet": 1000  // 这行可以删除
}
```

2. **设置交易金额**：
```json
{
    "stake_amount": 10,  // 每笔交易的USDT数量
    // 或者使用百分比
    "stake_amount": "5%"  // 使用5%的可用余额
}
```

3. **风险管理设置**：
```json
{
    "max_open_trades": 3,  // 最大同时持仓数
    "tradable_balance_ratio": 0.99,  // 可交易余额比例
    "stoploss": -0.05  // 止损比例 (5%)
}
```

## 6. 安全提醒

⚠️ **重要安全提示**：

1. **永远不要分享您的Secret Key**
2. **不要在公共代码库中提交包含密钥的配置文件**
3. **建议使用环境变量存储密钥**
4. **定期检查API使用情况**
5. **从小金额开始测试**

## 7. 环境变量配置（推荐）

为了更安全，可以使用环境变量：

```bash
# 设置环境变量
export BINANCE_API_KEY="your_api_key"
export BINANCE_SECRET_KEY="your_secret_key"
```

然后在配置文件中引用：
```json
{
    "exchange": {
        "name": "binance",
        "key": "${BINANCE_API_KEY}",
        "secret": "${BINANCE_SECRET_KEY}"
    }
}
```

## 8. 故障排除

### 常见错误：
- **Invalid API Key**: 检查API Key是否正确
- **IP not whitelisted**: 检查IP白名单设置
- **Insufficient permissions**: 检查API权限设置
- **Timestamp error**: 检查系统时间是否准确
