#!/bin/bash

# 设置代理环境变量
export http_proxy=http://127.0.0.1:7897
export https_proxy=http://127.0.0.1:7897
export HTTP_PROXY=http://127.0.0.1:7897
export HTTPS_PROXY=http://127.0.0.1:7897

echo "========================================="
echo "🚀 启动 Freqtrade WebUI (带代理)"
echo "========================================="
echo ""
echo "🌐 代理设置:"
echo "   HTTP Proxy: $http_proxy"
echo "   HTTPS Proxy: $https_proxy"
echo ""

# 激活conda环境
echo "📦 激活conda环境: freqtrade_conda"
source /Users/<USER>/anaconda3/bin/activate freqtrade_conda

# 显示版本信息
echo "📊 检查版本信息..."
freqtrade --version

echo ""
echo "🌐 启动 Freqtrade WebServer..."
echo "----------------------------------------"
echo "🔗 WebUI 地址: http://127.0.0.1:8080"
echo "👤 用户名: freqtrade"
echo "🔑 密码: freqtrade"
echo "📁 配置文件: user_data/config.json"
echo "🔄 模式: 模拟交易 (dry_run)"
echo "🌐 代理: 127.0.0.1:7897"
echo "----------------------------------------"
echo ""
echo "💡 提示: 按 Ctrl+C 停止服务器"
echo "💡 提示: 在浏览器中打开 http://127.0.0.1:8080"
echo ""

freqtrade webserver --config user_data/config.json
