# Freqtrade WebUI 安装和使用指南

## 环境信息

- **Python版本**: 3.10.18
- **Freqtrade版本**: 2025.5
- **Conda环境**: freqtrade_conda
- **操作系统**: macOS

## 已安装的组件

1. **Python虚拟环境**: 
   - 使用conda创建的独立环境 `freqtrade_conda`
   - 包含Python 3.10和所有必要的依赖

2. **Freqtrade**: 
   - 完整的加密货币交易机器人
   - 包含所有核心功能和依赖

3. **TA-Lib**: 
   - 技术分析库，用于计算技术指标
   - 通过conda-forge成功安装

4. **WebUI**: 
   - Freqtrade的Web界面
   - 提供图形化的交易管理界面

## 快速启动

### 方法1: 使用启动脚本
```bash
./start_freqtrade_webui.sh
```

### 方法2: 手动启动
```bash
# 激活conda环境
source /Users/<USER>/anaconda3/bin/activate freqtrade_conda

# 启动webserver
freqtrade webserver --config user_data/config.json
```

## 访问WebUI

启动成功后，在浏览器中访问：
- **URL**: http://127.0.0.1:8080
- **用户名**: freqtrade
- **密码**: freqtrade

## 配置说明

当前配置位于 `user_data/config.json`，主要设置：

- **交易模式**: 模拟交易 (dry_run: true)
- **交易所**: Binance
- **基础货币**: USDT
- **最大开仓数**: 3
- **交易对**: BTC/USDT, ETH/USDT, ADA/USDT, DOT/USDT, LTC/USDT

## 目录结构

```
freui/
├── freqtrade_env/          # Python虚拟环境 (备用)
├── user_data/              # Freqtrade用户数据
│   ├── config.json         # 主配置文件
│   ├── strategies/         # 交易策略
│   ├── data/              # 历史数据
│   ├── logs/              # 日志文件
│   └── ...
├── start_freqtrade_webui.sh # 启动脚本
└── README.md              # 本文件
```

## 常用命令

### 环境管理
```bash
# 激活conda环境
source /Users/<USER>/anaconda3/bin/activate freqtrade_conda

# 查看freqtrade版本
freqtrade --version

# 查看帮助
freqtrade --help
```

### 数据下载
```bash
# 下载历史数据
freqtrade download-data --config user_data/config.json --timerange 20240101-

# 下载特定交易对数据
freqtrade download-data --config user_data/config.json --pairs BTC/USDT ETH/USDT
```

### 回测
```bash
# 运行回测
freqtrade backtesting --config user_data/config.json --strategy SampleStrategy
```

## 注意事项

1. **安全提醒**: 当前配置为模拟交易模式，不会进行真实交易
2. **API密钥**: 如需真实交易，请在配置文件中添加交易所API密钥
3. **策略**: 可在 `user_data/strategies/` 目录下添加自定义交易策略
4. **日志**: 运行日志保存在 `user_data/logs/` 目录下

## 故障排除

如果遇到问题：

1. 确保conda环境已正确激活
2. 检查配置文件语法是否正确
3. 查看日志文件获取详细错误信息
4. 确保网络连接正常（用于获取市场数据）

## 下一步

1. 学习编写自定义交易策略
2. 配置真实交易所API（谨慎操作）
3. 设置Telegram通知
4. 优化交易参数和风险管理

---

**免责声明**: 加密货币交易存在风险，请谨慎操作并做好风险管理。
