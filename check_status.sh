#!/bin/bash

echo "========================================="
echo "📊 Freqtrade 状态检查"
echo "========================================="
echo ""

# 激活conda环境
source /Users/<USER>/anaconda3/bin/activate freqtrade_conda

# 检查freqtrade版本
echo "🔍 Freqtrade 版本信息:"
freqtrade --version
echo ""

# 检查UI版本
echo "🎨 UI 版本信息:"
freqtrade install-ui 2>/dev/null | grep "FreqUI Version" || echo "UI 已安装"
echo ""

# 检查配置文件
echo "⚙️  配置文件状态:"
if [ -f "user_data/config.json" ]; then
    echo "✅ 配置文件存在: user_data/config.json"
    
    # 检查是否为模拟交易
    if grep -q '"dry_run": true' user_data/config.json; then
        echo "🔄 模式: 模拟交易 (安全模式)"
    else
        echo "⚠️  模式: 真实交易 (请确保已配置API密钥)"
    fi
    
    # 检查API密钥配置
    if grep -q '"key": ""' user_data/config.json; then
        echo "🔑 API密钥: 未配置"
    else
        echo "🔑 API密钥: 已配置"
    fi
else
    echo "❌ 配置文件不存在"
fi
echo ""

# 检查webserver是否运行
echo "🌐 WebServer 状态:"
if curl -s http://127.0.0.1:8080/api/v1/ping > /dev/null 2>&1; then
    echo "✅ WebServer 正在运行"
    echo "🔗 访问地址: http://127.0.0.1:8080"
    echo "👤 用户名: freqtrade"
    echo "🔑 密码: freqtrade"
else
    echo "❌ WebServer 未运行"
    echo "💡 运行 ./start_freqtrade_webui.sh 启动服务器"
fi
echo ""

echo "========================================="
echo "📝 快速命令:"
echo "启动WebUI: ./start_freqtrade_webui.sh"
echo "查看帮助: freqtrade --help"
echo "测试配置: freqtrade test-pairlist --config user_data/config.json"
echo "========================================="
